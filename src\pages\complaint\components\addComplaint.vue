<script lang="ts" setup>
import { createComplaint } from '@/api/complaint'
import { getScenicList } from '@/api/config'
import { uploadToOSS } from '@/hooks/aliyun-oss'
import { useEnumStore } from '@/store'
import { useSafeAreaInsets } from '@/hooks/useSafeAreaInsets'
// 获取屏幕边界到安全区域距离

const insets = useSafeAreaInsets()
const enumsStore = useEnumStore()

const formRef = ref(null)
const formData = reactive<any>({})
const formRules = {}

onLoad(() => {
    getScenic()
})

const scenicList = ref<any>([])
const getScenic = async () => {
    const res: any = await getScenicList({})
    if (res.code === 200) {
        scenicList.value = res.data.map(item => {
            return { label: item.scenicName, value: item.id }
        })
    }
}

const handleFileChange = (files) => {
    console.log('文件列表变化:', files)
}

const handleSubmit = async () => {
    console.log('handleSubmit', formData)
}
</script>

<template>
    <view class="p-3">
        <wd-form ref="formRef" :model="formData" :rules="formRules">
            <wd-cell-group custom-class="rounded mb-3" :border="true">
                <wd-picker label="投诉景区" placeholder="请选择投诉景区" label-width="120rpx" prop="scenicId"
                    v-model="formData.scenicId" :columns="scenicList" />
                <wd-picker label="投诉类型" placeholder="请选择投诉类型" label-width="120rpx" prop="complaintType"
                    v-model="formData.complaintType" :columns="enumsStore.enumData?.complaintType?.list" />
                <wd-textarea label="评价" label-width="120rpx" prop="complaintContent" rows="3"
                    v-model="formData.complaintContent" placeholder="请填写评价" :maxlength="50" :show-word-limit="true" />
            </wd-cell-group>
            <wd-cell-group custom-class="rounded mb-3">
                <wd-cell title="上传照片（不超过9张）" prop="complaintImg" vertical>
                    <wd-upload :file-list="formData.complaintImg" @change="handleFileChange"></wd-upload>
                </wd-cell>
            </wd-cell-group>
            <wd-cell-group custom-class="rounded" :border="true">
                <wd-input label="联系人" label-width="120rpx" prop="name" v-model="formData.linkperson"
                    placeholder="请输入联系人"></wd-input>
                <wd-input label="联系电话" label-width="120rpx" prop="name" type="number" v-model="formData.linktel"
                    placeholder="请输入联系电话"></wd-input>
            </wd-cell-group>
        </wd-form>
        <view class="p-3 pb-safe">
            <wd-button type="primary" block @click="handleSubmit">提交</wd-button>
        </view>
    </view>
</template>

<style lang="scss" scoped>
//</style>

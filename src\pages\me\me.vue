<route lang="jsonc" type="page">{
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    // "navigationStyle": "custom",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTitleText": "我的"
  }
}</route>

<script lang="ts" setup>
import type { IUploadSuccessInfo } from '@/api/types/login'
import { storeToRefs } from 'pinia'
import { getUserInfo } from '@/api/login'
import { LOGIN_PAGE } from '@/router/config'
import { useUserStore } from '@/store'
import { useTokenStore } from '@/store/token'

const userStore = useUserStore()
// 使用storeToRefs解构userInfo
const { userInfo } = storeToRefs(userStore)

onLoad(() => {
  getInfo()
})
// #ifdef MP-WEIXIN
async function getInfo() {
  const res = await getUserInfo()
  useUserStore().setUserInfo(res.data)
}
// #endif
// 查看用户信息
function handleEditProfile() {
  uni.navigateTo({
    url: '/pages/me/editUserInfo',
  })
}

// 退出登录
function handleLogout() {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清空用户信息
        useTokenStore().logout()
        // 执行退出登录逻辑
        uni.showToast({
          title: '退出登录成功',
          icon: 'success',
        })
        // #ifdef MP-WEIXIN
        // 微信小程序，去首页
        uni.reLaunch({ url: '/pages/index/index' })
        // #endif
        // #ifndef MP-WEIXIN
        // 非微信小程序，去登录页
        uni.navigateTo({ url: LOGIN_PAGE })
        // #endif
      }
    },
  })
}

function goToPage(url: string) {
  uni.navigateTo({ url })
}
</script>

<template>
  <view class="p-3">
    <view class="flex items-center mb-4">
      <wd-img :src="userInfo.avatar" round width="90" height="90"></wd-img>
      <view class="ml-5">
        <view class="text-xl mb-1">{{ userInfo.nickname }}</view>
        <view class="text-sm c-coolGray" @click="handleEditProfile">查看并编辑个人资料</view>
      </view>
    </view>
    <view class="bg-white p-2 mb-4 rounded">
      <view class="font-bold mb-3">我的订单</view>
      <view class="grid grid-cols-4 gap-4">
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">门票订单</view>
        </view>
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">租赁订单</view>
        </view>
      </view>
    </view>
    <view class="bg-white p-2 mb-8 rounded">
      <view class="font-bold mb-3">更多服务</view>
      <view class="grid grid-cols-4 gap-4">
        <view class="text-center" @click="goToPage('/pages/tourist/index')">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">游客信息</view>
        </view>
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">游玩卡</view>
        </view>
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">一卡通</view>
        </view>
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">领券中心</view>
        </view>
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">我的优惠券</view>
        </view>
        <view class="text-center" @click="goToPage('/pages/complaint/index')">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">投诉建议</view>
        </view>
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">联系客服</view>
        </view>
        <view class="text-center">
          <wd-img :src="userInfo.avatar" round width="50" height="50" custom-class="mb-1"></wd-img>
          <view class="text-[12px]">投诉电话</view>
        </view>
      </view>
    </view>
    <view class="text-center w-full" v-if="useTokenStore().hasLogin">
      <wd-button type="primary" size="large" block @click="handleLogout">
        退出登录
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>

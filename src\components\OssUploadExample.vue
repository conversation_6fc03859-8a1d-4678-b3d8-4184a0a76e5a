<template>
  <view class="upload-example">
    <wd-cell-group custom-class="rounded mb-3">
      <wd-cell title="基础上传示例" vertical>
        <OssUpload
          v-model="basicFileList"
          :limit="3"
          :max-size="5"
          file-type-tag="basic"
          @success="handleUploadSuccess"
          @error="handleUploadError"
          @progress="handleUploadProgress"
        />
      </wd-cell>
    </wd-cell-group>

    <wd-cell-group custom-class="rounded mb-3">
      <wd-cell title="头像上传示例" vertical>
        <OssUpload
          v-model="avatarFileList"
          :limit="1"
          :max-size="2"
          :multiple="false"
          file-type-tag="avatar"
          accept="image/*"
          @success="handleAvatarSuccess"
        />
      </wd-cell>
    </wd-cell-group>

    <wd-cell-group custom-class="rounded mb-3">
      <wd-cell title="文档上传示例" vertical>
        <OssUpload
          v-model="docFileList"
          :limit="5"
          :max-size="10"
          file-type-tag="documents"
          accept=".pdf,.doc,.docx,.xls,.xlsx"
          @success="handleDocSuccess"
        />
      </wd-cell>
    </wd-cell-group>

    <view class="action-buttons">
      <wd-button type="primary" @click="getUploadedFiles">获取已上传文件</wd-button>
      <wd-button type="warning" @click="clearAllFiles">清空所有文件</wd-button>
      <wd-button type="success" @click="submitForm">提交表单</wd-button>
    </view>

    <!-- 显示上传结果 -->
    <view v-if="uploadedUrls.length > 0" class="result-section">
      <wd-cell-group custom-class="rounded">
        <wd-cell title="已上传文件URL" vertical>
          <view v-for="(url, index) in uploadedUrls" :key="index" class="url-item">
            <text class="url-text">{{ url }}</text>
          </view>
        </wd-cell>
      </wd-cell-group>
    </view>
  </view>
</template>

<script lang="ts" setup>
import OssUpload from './OssUpload.vue'

// 文件列表数据
const basicFileList = ref([])
const avatarFileList = ref([])
const docFileList = ref([])

// 已上传的文件URL列表
const uploadedUrls = ref<string[]>([])

// 组件引用
const basicUploadRef = ref()
const avatarUploadRef = ref()
const docUploadRef = ref()

/**
 * 基础上传成功回调
 */
const handleUploadSuccess = (file: any, fileList: any[]) => {
  console.log('基础上传成功:', file, fileList)
  uni.showToast({
    title: '上传成功',
    icon: 'success'
  })
}

/**
 * 上传失败回调
 */
const handleUploadError = (error: any, file: any, fileList: any[]) => {
  console.error('上传失败:', error, file, fileList)
  uni.showToast({
    title: '上传失败',
    icon: 'error'
  })
}

/**
 * 上传进度回调
 */
const handleUploadProgress = (percent: number, file: any) => {
  console.log(`上传进度: ${percent}%`, file)
}

/**
 * 头像上传成功回调
 */
const handleAvatarSuccess = (file: any, fileList: any[]) => {
  console.log('头像上传成功:', file)
  uni.showToast({
    title: '头像上传成功',
    icon: 'success'
  })
}

/**
 * 文档上传成功回调
 */
const handleDocSuccess = (file: any, fileList: any[]) => {
  console.log('文档上传成功:', file)
  uni.showToast({
    title: '文档上传成功',
    icon: 'success'
  })
}

/**
 * 获取所有已上传的文件
 */
const getUploadedFiles = () => {
  const allUrls: string[] = []
  
  // 收集所有上传组件的文件URL
  basicFileList.value.forEach((file: any) => {
    if (file.status === 'success' && file.url) {
      allUrls.push(file.url)
    }
  })
  
  avatarFileList.value.forEach((file: any) => {
    if (file.status === 'success' && file.url) {
      allUrls.push(file.url)
    }
  })
  
  docFileList.value.forEach((file: any) => {
    if (file.status === 'success' && file.url) {
      allUrls.push(file.url)
    }
  })
  
  uploadedUrls.value = allUrls
  
  console.log('所有已上传文件URL:', allUrls)
  
  if (allUrls.length > 0) {
    uni.showToast({
      title: `共${allUrls.length}个文件`,
      icon: 'success'
    })
  } else {
    uni.showToast({
      title: '暂无已上传文件',
      icon: 'none'
    })
  }
}

/**
 * 清空所有文件
 */
const clearAllFiles = () => {
  basicFileList.value = []
  avatarFileList.value = []
  docFileList.value = []
  uploadedUrls.value = []
  
  uni.showToast({
    title: '已清空所有文件',
    icon: 'success'
  })
}

/**
 * 提交表单
 */
const submitForm = () => {
  getUploadedFiles()
  
  const formData = {
    basicFiles: basicFileList.value.filter(f => f.status === 'success').map(f => f.url),
    avatar: avatarFileList.value.find(f => f.status === 'success')?.url || '',
    documents: docFileList.value.filter(f => f.status === 'success').map(f => f.url)
  }
  
  console.log('提交表单数据:', formData)
  
  // 这里可以调用API提交数据
  uni.showModal({
    title: '提交成功',
    content: `基础文件: ${formData.basicFiles.length}个\n头像: ${formData.avatar ? '1个' : '0个'}\n文档: ${formData.documents.length}个`,
    showCancel: false
  })
}
</script>

<style lang="scss" scoped>
.upload-example {
  padding: 20rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin: 40rpx 0;
  
  .wd-button {
    flex: 1;
  }
}

.result-section {
  margin-top: 40rpx;
}

.url-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.url-text {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}
</style>

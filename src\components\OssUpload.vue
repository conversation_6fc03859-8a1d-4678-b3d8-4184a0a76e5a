<template>
  <wd-upload
    :file-list="fileList"
    :limit="limit"
    :max-size="maxSize"
    :accept="accept"
    :disabled="disabled"
    :preview="preview"
    :multiple="multiple"
    @change="handleChange"
    @remove="handleRemove"
    @click="handleClick"
    @preview="handlePreview"
    @oversize="handleOversize"
    @error="handleError"
  >
  </wd-upload>
</template>

<script lang="ts" setup>
import { uploadToOSS } from '@/hooks/aliyun-oss'

interface FileItem {
  uid?: string
  url?: string
  name?: string
  status?: 'uploading' | 'success' | 'error'
  response?: any
  percent?: number
  size?: number
  type?: string
  tempFilePath?: string
}

interface Props {
  /** 文件列表 */
  modelValue?: FileItem[]
  /** 最大上传数量 */
  limit?: number
  /** 文件大小限制，单位MB */
  maxSize?: number
  /** 接受的文件类型 */
  accept?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否支持预览 */
  preview?: boolean
  /** 是否支持多选 */
  multiple?: boolean
  /** 文件类型标签，用于OSS路径分类 */
  fileTypeTag?: string
  /** 上传成功回调 */
  onSuccess?: (file: FileItem, fileList: FileItem[]) => void
  /** 上传失败回调 */
  onError?: (error: any, file: FileItem, fileList: FileItem[]) => void
  /** 上传进度回调 */
  onProgress?: (percent: number, file: FileItem) => void
  /** 文件移除回调 */
  onRemove?: (file: FileItem, fileList: FileItem[]) => void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  limit: 9,
  maxSize: 10,
  accept: 'image/*',
  disabled: false,
  preview: true,
  multiple: true,
  fileTypeTag: 'common'
})

const emit = defineEmits<{
  'update:modelValue': [value: FileItem[]]
  'change': [fileList: FileItem[]]
  'success': [file: FileItem, fileList: FileItem[]]
  'error': [error: any, file: FileItem, fileList: FileItem[]]
  'progress': [percent: number, file: FileItem]
  'remove': [file: FileItem, fileList: FileItem[]]
}>()

const fileList = computed({
  get: () => props.modelValue || [],
  set: (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

/**
 * 生成唯一ID
 */
function generateUID(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 处理文件选择变化
 */
const handleChange = async (files: any) => {
  console.log('文件选择变化:', files)
  
  // 获取新增的文件
  const newFiles = files.filter((file: any) => !file.url && file.tempFilePath)
  
  if (newFiles.length === 0) {
    fileList.value = files
    return
  }

  // 处理新增文件上传
  for (const file of newFiles) {
    const fileItem: FileItem = {
      uid: generateUID(),
      name: file.name || `file_${Date.now()}`,
      status: 'uploading',
      percent: 0,
      size: file.size,
      type: file.type,
      tempFilePath: file.tempFilePath
    }

    // 添加到文件列表
    const currentList = [...fileList.value]
    const existingIndex = currentList.findIndex(item => item.tempFilePath === file.tempFilePath)
    
    if (existingIndex >= 0) {
      currentList[existingIndex] = fileItem
    } else {
      currentList.push(fileItem)
    }
    
    fileList.value = currentList

    try {
      // 上传到OSS
      const ossUrl = await uploadToOSS({
        filePath: file.tempFilePath,
        fileTypeTag: props.fileTypeTag,
        fileName: file.name,
        onProgress: (percent) => {
          fileItem.percent = percent
          props.onProgress?.(percent, fileItem)
          emit('progress', percent, fileItem)
          
          // 更新文件列表中的进度
          const updatedList = fileList.value.map(item => 
            item.uid === fileItem.uid ? { ...item, percent } : item
          )
          fileList.value = updatedList
        }
      })

      // 上传成功
      fileItem.status = 'success'
      fileItem.url = ossUrl
      fileItem.percent = 100
      
      const successList = fileList.value.map(item => 
        item.uid === fileItem.uid ? { ...item, status: 'success', url: ossUrl, percent: 100 } : item
      )
      fileList.value = successList

      props.onSuccess?.(fileItem, successList)
      emit('success', fileItem, successList)

    } catch (error) {
      console.error('上传失败:', error)
      
      // 上传失败
      fileItem.status = 'error'
      
      const errorList = fileList.value.map(item => 
        item.uid === fileItem.uid ? { ...item, status: 'error' } : item
      )
      fileList.value = errorList

      props.onError?.(error, fileItem, errorList)
      emit('error', error, fileItem, errorList)

      // 显示错误提示
      uni.showToast({
        title: '上传失败',
        icon: 'none'
      })
    }
  }
}

/**
 * 处理文件移除
 */
const handleRemove = (file: any) => {
  console.log('移除文件:', file)
  
  const newList = fileList.value.filter(item => item.uid !== file.uid)
  fileList.value = newList
  
  props.onRemove?.(file, newList)
  emit('remove', file, newList)
}

/**
 * 处理点击事件
 */
const handleClick = () => {
  console.log('点击上传区域')
}

/**
 * 处理预览
 */
const handlePreview = (file: any) => {
  console.log('预览文件:', file)
  
  if (file.url) {
    uni.previewImage({
      urls: [file.url],
      current: file.url
    })
  }
}

/**
 * 处理文件超大小
 */
const handleOversize = (file: any) => {
  console.log('文件超大小:', file)
  
  uni.showToast({
    title: `文件大小不能超过${props.maxSize}MB`,
    icon: 'none'
  })
}

/**
 * 处理上传错误
 */
const handleError = (error: any) => {
  console.error('上传组件错误:', error)
  
  uni.showToast({
    title: '上传出错',
    icon: 'none'
  })
}

/**
 * 获取已上传成功的文件URL列表
 */
const getUploadedUrls = (): string[] => {
  return fileList.value
    .filter(file => file.status === 'success' && file.url)
    .map(file => file.url!)
}

/**
 * 清空文件列表
 */
const clearFiles = () => {
  fileList.value = []
}

/**
 * 手动触发上传
 */
const submit = () => {
  // 可以在这里实现手动上传逻辑
  console.log('手动触发上传')
}

// 暴露方法给父组件
defineExpose({
  getUploadedUrls,
  clearFiles,
  submit
})
</script>

<style lang="scss" scoped>
// 可以在这里添加自定义样式
</style>

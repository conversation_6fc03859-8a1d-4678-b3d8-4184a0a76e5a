<route lang="jsonc" type="page">{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "登录"
  }
}</route>

<script lang="ts" setup>
import { useTokenStore } from '@/store/token'
import { useUserStore } from '@/store/user'
import { useEnumStore } from '@/store'
import { tabbarList } from '@/tabbar/config'
import { ensureDecodeURIComponent } from '@/utils'

const redirectUrl = ref('')
onLoad((options) => {
  console.log('login options: ', options)
  if (options.redirect) {
    redirectUrl.value = ensureDecodeURIComponent(options.redirect)
  }
  else {
    redirectUrl.value = tabbarList[0].pagePath
  }
  console.log('redirectUrl.value: ', redirectUrl.value)
})

const userStore = useUserStore()
const tokenStore = useTokenStore()
const enumStore = useEnumStore()
// 微信小程序下登录
async function handleLogin() {
  // #ifdef MP-WEIXIN

  // 微信登录
  const res = await tokenStore.wxLogin()
  console.log('res: ', res)
  if (res.code === 200) {
    // 关闭当前登录页，返回上一页
    enumStore.getEnumData()
    uni.navigateBack()
  }
  // #endif
  // #ifndef MP-WEIXIN
  // uni.navigateTo({ url: LOGIN_PAGE })
  // #endif
}
</script>

<template>
  <view class="login">
    <button @click="handleLogin">
      一键登录
    </button>
  </view>
</template>

<style lang="scss" scoped>
//</style>

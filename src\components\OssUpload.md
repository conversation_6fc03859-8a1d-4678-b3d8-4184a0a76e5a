# OssUpload 自定义上传组件

基于 wd-upload 组件和 aliyun-oss.ts 封装的自定义上传组件，支持直接上传文件到阿里云 OSS。

## 特性

- 🚀 基于 wd-upload 组件，保持原有 UI 和交互体验
- ☁️ 自动上传到阿里云 OSS，无需手动处理
- 📁 支持文件类型标签分类存储
- 📊 实时上传进度显示
- 🔄 支持上传失败重试
- 📱 支持多平台（H5、小程序、App）

## 基础用法

```vue
<template>
  <OssUpload
    v-model="fileList"
    :limit="9"
    :max-size="5"
    file-type-tag="complaint"
    @success="handleSuccess"
    @error="handleError"
  />
</template>

<script setup>
import OssUpload from '@/components/OssUpload.vue'

const fileList = ref([])

const handleSuccess = (file, fileList) => {
  console.log('上传成功:', file.url)
}

const handleError = (error, file, fileList) => {
  console.error('上传失败:', error)
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | FileItem[] | [] | 文件列表，支持 v-model |
| limit | number | 9 | 最大上传数量 |
| maxSize | number | 10 | 文件大小限制，单位 MB |
| accept | string | 'image/*' | 接受的文件类型 |
| disabled | boolean | false | 是否禁用 |
| preview | boolean | true | 是否支持预览 |
| multiple | boolean | true | 是否支持多选 |
| fileTypeTag | string | 'common' | 文件类型标签，用于 OSS 路径分类 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| success | (file, fileList) | 上传成功时触发 |
| error | (error, file, fileList) | 上传失败时触发 |
| progress | (percent, file) | 上传进度变化时触发 |
| remove | (file, fileList) | 移除文件时触发 |
| change | (fileList) | 文件列表变化时触发 |

## 方法

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getUploadedUrls | - | string[] | 获取已上传成功的文件 URL 列表 |
| clearFiles | - | - | 清空文件列表 |
| submit | - | - | 手动触发上传（预留） |

## 使用示例

### 头像上传

```vue
<template>
  <OssUpload
    v-model="avatarList"
    :limit="1"
    :max-size="2"
    :multiple="false"
    file-type-tag="avatar"
    accept="image/*"
    @success="handleAvatarSuccess"
  />
</template>

<script setup>
const avatarList = ref([])

const handleAvatarSuccess = (file) => {
  console.log('头像上传成功:', file.url)
  // 可以将 file.url 保存到用户信息中
}
</script>
```

### 文档上传

```vue
<template>
  <OssUpload
    v-model="docList"
    :limit="5"
    :max-size="10"
    file-type-tag="documents"
    accept=".pdf,.doc,.docx,.xls,.xlsx"
    @success="handleDocSuccess"
  />
</template>

<script setup>
const docList = ref([])

const handleDocSuccess = (file, fileList) => {
  console.log('文档上传成功:', file.url)
  // 获取所有已上传的文档 URL
  const urls = fileList
    .filter(f => f.status === 'success')
    .map(f => f.url)
  console.log('所有文档 URL:', urls)
}
</script>
```

### 获取上传结果

```vue
<template>
  <OssUpload ref="uploadRef" v-model="fileList" />
  <button @click="getUrls">获取已上传文件</button>
</template>

<script setup>
const uploadRef = ref()
const fileList = ref([])

const getUrls = () => {
  const urls = uploadRef.value.getUploadedUrls()
  console.log('已上传文件 URL:', urls)
}
</script>
```

## 注意事项

1. **文件大小单位**：maxSize 参数单位为 MB，组件内部会自动转换为字节传给 wd-upload
2. **文件类型标签**：fileTypeTag 用于在 OSS 中分类存储文件，建议使用有意义的标签如 'avatar'、'documents' 等
3. **上传进度**：组件会自动显示上传进度，无需额外处理
4. **错误处理**：上传失败时会自动显示错误提示，也可以通过 @error 事件自定义处理
5. **文件预览**：图片文件支持点击预览，使用 uni.previewImage API

## 文件状态

FileItem 对象包含以下状态：

- `uploading`: 上传中
- `success`: 上传成功
- `error`: 上传失败

可以根据状态来判断文件的上传情况。

<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarBackgroundColor": "#ffffff",
        "navigationBarTitleText": "游客信息"
    }
}</route>

<script lang="ts" setup>
import { getTourists, updateTourist, createTourist, deleteTourist, setDefaultTourist } from '@/api/tourist'
import type { FormItemRule } from 'wot-design-uni/components/wd-form/types'
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
import { useSafeAreaInsets } from '@/hooks/useSafeAreaInsets'
// 获取屏幕边界到安全区域距离

const insets = useSafeAreaInsets()

const listData = ref([])
const scrollRef = ref(null)
UseZPaging(scrollRef)

// 获取游客列表
const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getTourists({
            page: pageNo,
            pageSize: pageSize
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}

// 弹窗相关
const showPopup = ref(false)
const formRef = ref()
const formData = ref<any>({
    name: '',
    phone: '',
    idcard: ''
})

// 表单验证规则
const formRules: Record<string, FormItemRule[]> = {
    name: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { required: false, min: 2, max: 20, message: '姓名长度为2-20个字符', trigger: 'blur' }
    ],
    phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { required: false, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    idcard: [
        { required: true, message: '请输入身份证号', trigger: 'blur' },
        { required: false, pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
    ]
}

// 编辑游客信息
const handleEdit = (item: any) => {
    formData.value = {
        name: item.name,
        phone: item.phone,
        idcard: item.idcard,
        id: item.id
    }
    showPopup.value = true
}

// 删除游客信息
const handleDelete = (item: any) => {
    uni.showModal({
        title: '提示',
        content: `确定要删除游客 ${item.name} 吗？`,
        success: async (res) => {
            if (res.confirm) {
                try {
                    const result: any = await deleteTourist({ id: item.id })
                    if (result.code === 200) {
                        uni.showToast({
                            title: '删除成功',
                            icon: 'success'
                        })
                        scrollRef.value?.reload()
                    } else {
                        uni.showToast({
                            title: result.message || '删除失败',
                            icon: 'none'
                        })
                    }
                } catch (error) {
                    uni.showToast({
                        title: '删除失败',
                        icon: 'none'
                    })
                }
            }
        }
    })
}

// 设置默认游客
const handleSetDefault = async (item: any) => {
    // 如果已经是默认的，则不需要再次设置
    if (item.isDefault) return

    try {
        const result: any = await setDefaultTourist(item.id)
        if (result.code === 200) {
            uni.showToast({
                title: '设置成功',
                icon: 'success'
            })
            scrollRef.value?.reload()
        } else {
            uni.showToast({
                title: result.message || '设置失败',
                icon: 'none'
            })
        }
    } catch (error) {
        uni.showToast({
            title: '设置失败',
            icon: 'none'
        })
    }
}

// 处理checkbox变化事件
const handleCheckboxChange = (e: any, item: any) => {
    // 如果是取消选中或者已经是默认项，则不处理
    if (!e.value || item.isDefault) return
    handleSetDefault(item)
}

// 关闭弹窗
const handleClose = () => {
    formData.value = {
        name: '',
        phone: '',
        idcard: ''
    }
    showPopup.value = false
}

// 提交表单
const handleSubmit = async () => {
    // 表单验证
    const isValid = await new Promise((resolve) => {
        formRef.value?.validate().then(({ valid, errors }) => {
            if (!valid) {
                uni.showToast({
                    title: errors?.[0]?.message || '表单验证失败',
                    icon: 'none'
                })
            }
            resolve(valid)
        })
    })

    if (!isValid) return

    try {
        let res: any
        if (!formData.value.id) {
            // 新增游客
            res = await createTourist(formData.value)
        } else {
            // 更新游客
            res = await updateTourist(formData.value)
        }

        if (res.code === 200) {
            uni.showToast({
                title: '保存成功',
                icon: 'success'
            })
            handleClose()
            scrollRef.value?.reload()
        } else {
            uni.showToast({
                title: res.message || '保存失败',
                icon: 'none'
            })
        }
    } catch (error) {
        uni.showToast({
            title: error.data.message || '保存失败',
            icon: 'none'
        })
    }
}
</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :show-loading-more-no-more-view="false">
        <view class="p-3">
            <view class="bg-white p-3 rounded mb-4 last:mb-0" v-for="item in listData" :key="item.id">
                <view class="flex items-center justify-between mb-2">
                    <view class="">
                        <view class="mb-2 flex items-center">
                            <text class="text-md mr-3">{{ item.name }}</text>
                            <wd-tag type="primary" mark v-if="item.isDefault">默认</wd-tag>
                        </view>
                        <view class="text-sm"><text class="mr-2 text-gray">身份证</text>{{ item.idcard }}</view>
                        <view class="text-sm"><text class="mr-2 text-gray">手机号</text>{{ item.phone }}</view>
                    </view>
                    <view class="px-2">
                        <wd-icon name="edit-1" @click="handleEdit(item)" />
                    </view>
                </view>
                <view class="flex justify-between">
                    <wd-checkbox :model-value="item.isDefault" :true-value="1" :false-value="0"
                        @change="(e) => handleCheckboxChange(e, item)">
                        {{ item.isDefault ? '默认' : '设为默认' }}
                    </wd-checkbox>
                    <wd-icon name="delete" color="#fa4350" custom-class="px-2 text-red" @click="handleDelete(item)"
                        v-if="!item.isDefault" />
                </view>
            </view>
        </view>
        <template #bottom>
            <view class="p-3 bg-white  pb-safe"><wd-button type="primary" block @click="showPopup = true">添加游客</wd-button>
            </view>
        </template>
    </z-paging>

    <wd-popup v-model="showPopup" position="bottom" :closable="true" custom-style="height: auto;" @close="handleClose">
        <view class="text-center leading-[2.8] font-bold">{{ formData.id ? '编辑游客' : '添加游客' }}</view>
        <view :style="{ paddingBottom: `${insets?.bottom}px` }">
            <wd-form ref="formRef" :model="formData" :rules="formRules">
                <wd-cell-group :border="true">
                    <wd-input label="姓名" label-width="150rpx" prop="name" v-model="formData.name" placeholder="请输入姓名"
                        clearable />
                    <wd-input label="手机号" label-width="150rpx" prop="phone" v-model="formData.phone"
                        placeholder="请输入手机号" clearable />
                    <wd-input label="身份证号" label-width="150rpx" prop="idcard" type="idcard" v-model="formData.idcard"
                        placeholder="请输入身份证号" clearable />
                    <wd-cell title-width="0px">
                        <view class="mt-2 px-3">
                            <wd-button type="primary" block @click="handleSubmit">保存</wd-button>
                        </view>
                    </wd-cell>
                </wd-cell-group>
            </wd-form>
        </view>
    </wd-popup>
</template>

<style lang="scss" scoped></style>